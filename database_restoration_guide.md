# دليل استعادة قاعدة البيانات - نظام إدارة المراكز الصحية

## 📋 نظرة عامة
هذا الدليل يوضح كيفية استعادة قاعدة البيانات المحذوفة لنظام إدارة المراكز الصحية باستخدام ملف `database_complete_rebuild.sql` على خادم Hostinger مع CloudPanel.

---

## 🔧 الخطوات التحضيرية

### 1. التحقق من متطلبات النظام

#### متطلبات الخادم:
- **MySQL**: الإصدار 5.7 أو أحدث
- **MariaDB**: الإصدار 10.2 أو أحدث
- **PHP**: الإصدار 7.4 أو أحدث
- **الترميز**: دعم UTF-8/UTF8MB4

#### التحقق من إصدار MySQL/MariaDB:
```bash
mysql --version
# أو
mysql -u root -p -e "SELECT VERSION();"
```

#### التحقق من دعم UTF8MB4:
```sql
SHOW CHARACTER SET LIKE 'utf8mb4';
SHOW COLLATION LIKE 'utf8mb4_unicode_ci';
```

### 2. إعداد البيئة

#### أ) تحضير ملفات الاستعادة:
```bash
# التأكد من وجود الملف
ls -la database_complete_rebuild.sql

# التحقق من حجم الملف
du -h database_complete_rebuild.sql

# التحقق من صحة الملف
head -10 database_complete_rebuild.sql
tail -10 database_complete_rebuild.sql
```

#### ب) إعداد متغيرات البيئة:
```bash
# تعيين متغيرات قاعدة البيانات
export DB_HOST="localhost"
export DB_NAME="csdb"
export DB_USER="csdbuser"
export DB_PASS="j5aKN6lz5bsujTcWaYAd"
```

#### ج) إنشاء نسخة احتياطية من الوضع الحالي (إن وجد):
```bash
# إنشاء مجلد للنسخ الاحتياطية
mkdir -p backups/$(date +%Y%m%d_%H%M%S)

# نسخ احتياطية للملفات الحالية
cp -r config/ backups/$(date +%Y%m%d_%H%M%S)/
```

---

## 🚀 طرق الاستعادة

### الطريقة الأولى: عبر سطر الأوامر (الأسرع والأكثر موثوقية)

#### 1. الاتصال بـ MySQL:
```bash
mysql -h localhost -u csdbuser -p
```

#### 2. إنشاء قاعدة البيانات:
```sql
-- حذف قاعدة البيانات إن كانت موجودة (احذر!)
DROP DATABASE IF EXISTS csdb;

-- إنشاء قاعدة البيانات الجديدة
CREATE DATABASE csdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- التحقق من الإنشاء
SHOW DATABASES LIKE 'csdb';

-- الخروج من MySQL
EXIT;
```

#### 3. استيراد البيانات:
```bash
# الطريقة الأولى: استيراد مباشر
mysql -h localhost -u csdbuser -p csdb < database_complete_rebuild.sql

# الطريقة الثانية: مع عرض التقدم
pv database_complete_rebuild.sql | mysql -h localhost -u csdbuser -p csdb

# الطريقة الثالثة: مع تسجيل الأخطاء
mysql -h localhost -u csdbuser -p csdb < database_complete_rebuild.sql 2> import_errors.log
```

#### 4. التحقق من نجاح الاستيراد:
```bash
mysql -h localhost -u csdbuser -p csdb -e "SHOW TABLES;"
mysql -h localhost -u csdbuser -p csdb -e "SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema='csdb';"
```

### الطريقة الثانية: عبر CloudPanel

#### 1. الوصول إلى CloudPanel:
- افتح متصفح الويب
- اذهب إلى: `https://your-server-ip:8443`
- سجل الدخول بحساب CloudPanel

#### 2. إدارة قواعد البيانات:
1. انقر على "Databases" في القائمة الجانبية
2. انقر على "MySQL Databases"
3. انقر على "Add Database"
4. أدخل اسم قاعدة البيانات: `csdb`
5. اختر الترميز: `utf8mb4_unicode_ci`
6. انقر على "Add Database"

#### 3. إنشاء مستخدم قاعدة البيانات:
1. انقر على "Add User"
2. اسم المستخدم: `csdbuser`
3. كلمة المرور: `j5aKN6lz5bsujTcWaYAd`
4. اختر قاعدة البيانات: `csdb`
5. امنح جميع الصلاحيات
6. انقر على "Add User"

#### 4. استيراد البيانات:
1. انقر على اسم قاعدة البيانات `csdb`
2. انقر على "Import"
3. اختر ملف `database_complete_rebuild.sql`
4. تأكد من اختيار الترميز: `utf8mb4`
5. انقر على "Import"

### الطريقة الثالثة: عبر phpMyAdmin

#### 1. الوصول إلى phpMyAdmin:
```
https://your-domain.com/phpmyadmin
# أو
https://your-server-ip/phpmyadmin
```

#### 2. إنشاء قاعدة البيانات:
1. انقر على "Databases" في الأعلى
2. أدخل اسم قاعدة البيانات: `csdb`
3. اختر الترميز: `utf8mb4_unicode_ci`
4. انقر على "Create"

#### 3. استيراد البيانات:
1. اختر قاعدة البيانات `csdb` من القائمة اليسرى
2. انقر على تبويب "Import"
3. انقر على "Choose File" واختر `database_complete_rebuild.sql`
4. تأكد من الإعدادات:
   - Format: SQL
   - Character set: utf8mb4
   - Partial import: غير مفعل
5. انقر على "Go"

### الطريقة الرابعة: عبر أدوات إدارة قواعد البيانات

#### MySQL Workbench:
1. افتح MySQL Workbench
2. أنشئ اتصال جديد:
   - Connection Name: Healthcare System
   - Hostname: your-server-ip
   - Port: 3306
   - Username: csdbuser
   - Password: j5aKN6lz5bsujTcWaYAd
3. اختبر الاتصال
4. افتح الاتصال
5. انقر على "File" > "Run SQL Script"
6. اختر ملف `database_complete_rebuild.sql`
7. انقر على "Start Import"

#### HeidiSQL:
1. افتح HeidiSQL
2. أنشئ جلسة جديدة:
   - Network type: MySQL (TCP/IP)
   - Hostname: your-server-ip
   - User: csdbuser
   - Password: j5aKN6lz5bsujTcWaYAd
   - Port: 3306
3. انقر على "Open"
4. انقر بالزر الأيمن على الخادم > "Create new" > "Database"
5. اسم قاعدة البيانات: `csdb`
6. الترميز: `utf8mb4_unicode_ci`
7. انقر على "File" > "Load SQL file"
8. اختر `database_complete_rebuild.sql`
9. انقر على "Execute"

---

## ✅ التحقق من نجاح عملية الاستعادة

### 1. فحص الجداول:
```sql
-- الاتصال بقاعدة البيانات
USE csdb;

-- عرض جميع الجداول
SHOW TABLES;

-- التحقق من عدد الجداول (يجب أن يكون 22)
SELECT COUNT(*) as total_tables 
FROM information_schema.tables 
WHERE table_schema = 'csdb';
```

### 2. فحص البيانات الأساسية:
```sql
-- فحص المراكز الصحية (يجب أن يكون 3)
SELECT COUNT(*) as centers_count FROM centers;
SELECT * FROM centers;

-- فحص اللقاحات (يجب أن يكون 19)
SELECT COUNT(*) as vaccines_count FROM vaccines;
SELECT id, name_ar, age_months FROM vaccines ORDER BY age_months;

-- فحص الأدوية (يجب أن يكون 8)
SELECT COUNT(*) as medicines_count FROM medicines;
SELECT id, name, unit FROM medicines;

-- فحص وسائل منع الحمل (يجب أن يكون 10)
SELECT COUNT(*) as contraceptives_count FROM contraceptives;
SELECT id, name, type FROM contraceptives;

-- فحص المستخدمين (يجب أن يكون 4)
SELECT COUNT(*) as users_count FROM users;
SELECT id, username, name, role FROM users;
```

### 3. فحص العلاقات والفهارس:
```sql
-- فحص المفاتيح الخارجية
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = 'csdb'
ORDER BY TABLE_NAME;

-- فحص الفهارس
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'csdb'
ORDER BY TABLE_NAME, INDEX_NAME;
```

### 4. فحص الترميز:
```sql
-- فحص ترميز قاعدة البيانات
SELECT 
    SCHEMA_NAME,
    DEFAULT_CHARACTER_SET_NAME,
    DEFAULT_COLLATION_NAME
FROM information_schema.SCHEMATA
WHERE SCHEMA_NAME = 'csdb';

-- فحص ترميز الجداول
SELECT 
    TABLE_NAME,
    TABLE_COLLATION
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'csdb';
```

### 5. اختبار Views:
```sql
-- فحص الـ Views المُنشأة
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- اختبار view الأطفال مع الممرضين
SELECT COUNT(*) FROM children_with_nurse;

-- اختبار view ملخص المخزون
SELECT item_type, COUNT(*) as count FROM stock_summary GROUP BY item_type;
```

---

## 🔧 استكشاف الأخطاء الشائعة وحلولها

### 1. خطأ في الاتصال بقاعدة البيانات

#### الخطأ:
```
ERROR 1045 (28000): Access denied for user 'csdbuser'@'localhost'
```

#### الحلول:
```sql
-- التحقق من وجود المستخدم
SELECT User, Host FROM mysql.user WHERE User = 'csdbuser';

-- إنشاء المستخدم إذا لم يكن موجوداً
CREATE USER 'csdbuser'@'localhost' IDENTIFIED BY 'j5aKN6lz5bsujTcWaYAd';
CREATE USER 'csdbuser'@'%' IDENTIFIED BY 'j5aKN6lz5bsujTcWaYAd';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON csdb.* TO 'csdbuser'@'localhost';
GRANT ALL PRIVILEGES ON csdb.* TO 'csdbuser'@'%';
FLUSH PRIVILEGES;
```

### 2. خطأ في الترميز

#### الخطأ:
```
ERROR 1273 (HY000): Unknown collation: 'utf8mb4_unicode_ci'
```

#### الحلول:
```sql
-- التحقق من دعم UTF8MB4
SHOW CHARACTER SET LIKE 'utf8mb4';

-- إذا لم يكن مدعوماً، استخدم UTF8
CREATE DATABASE csdb CHARACTER SET utf8 COLLATE utf8_unicode_ci;

-- أو تحديث MySQL/MariaDB
```

### 3. خطأ في حجم الملف

#### الخطأ:
```
ERROR 2006 (HY000): MySQL server has gone away
```

#### الحلول:
```sql
-- زيادة قيم المهلة الزمنية
SET GLOBAL max_allowed_packet = 1073741824; -- 1GB
SET GLOBAL wait_timeout = 28800; -- 8 hours
SET GLOBAL interactive_timeout = 28800;

-- أو تقسيم الملف إلى أجزاء أصغر
split -l 1000 database_complete_rebuild.sql part_
```

### 4. خطأ في المفاتيح الخارجية

#### الخطأ:
```
ERROR 1215 (HY000): Cannot add foreign key constraint
```

#### الحلول:
```sql
-- تعطيل فحص المفاتيح الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- تشغيل الاستيراد
SOURCE database_complete_rebuild.sql;

-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;
```

### 5. خطأ في الصلاحيات

#### الخطأ:
```
ERROR 1142 (42000): CREATE command denied to user
```

#### الحلول:
```sql
-- منح صلاحيات إضافية
GRANT CREATE, DROP, ALTER, INDEX ON csdb.* TO 'csdbuser'@'localhost';
GRANT CREATE VIEW ON csdb.* TO 'csdbuser'@'localhost';
FLUSH PRIVILEGES;
```

### 6. خطأ في مساحة القرص

#### الخطأ:
```
ERROR 1114 (HY000): The table is full
```

#### الحلول:
```bash
# فحص مساحة القرص
df -h

# تنظيف الملفات المؤقتة
sudo rm -rf /tmp/mysql*
sudo rm -rf /var/tmp/mysql*

# زيادة مساحة tmpdir في MySQL
```

---

## ⚙️ خطوات ما بعد الاستعادة

### 1. إعداد الصلاحيات النهائية

```sql
-- إنشاء مستخدم للتطبيق
CREATE USER 'app_user'@'localhost' IDENTIFIED BY 'secure_password_here';

-- منح صلاحيات محدودة للتطبيق
GRANT SELECT, INSERT, UPDATE, DELETE ON csdb.* TO 'app_user'@'localhost';

-- إنشاء مستخدم للقراءة فقط (للتقارير)
CREATE USER 'readonly_user'@'localhost' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON csdb.* TO 'readonly_user'@'localhost';

-- إنشاء مستخدم للنسخ الاحتياطي
CREATE USER 'backup_user'@'localhost' IDENTIFIED BY 'backup_password';
GRANT SELECT, LOCK TABLES, SHOW VIEW ON csdb.* TO 'backup_user'@'localhost';

FLUSH PRIVILEGES;
```

### 2. تحديث ملفات التهيئة

#### تحديث config/database.php:
```php
<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'csdb';
    private $username = 'csdbuser';
    private $password = 'j5aKN6lz5bsujTcWaYAd';
    private $charset = 'utf8mb4';

    // باقي الكود...
}
?>
```

#### تحديث config/database-live.php:
```php
<?php
$db_configs = [
    [
        'host' => 'localhost',
        'dbname' => 'csdb',
        'username' => 'csdbuser',
        'password' => 'j5aKN6lz5bsujTcWaYAd',
        'charset' => 'utf8mb4'
    ]
];
?>
```

### 3. اختبار الاتصال من التطبيق

```php
<?php
// ملف اختبار الاتصال: test_connection.php
require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();

    echo "✅ الاتصال بقاعدة البيانات نجح!\n";

    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✅ عدد المستخدمين: " . $result['count'] . "\n";

    // اختبار الترميز العربي
    $stmt = $pdo->query("SELECT name_ar FROM vaccines LIMIT 1");
    $result = $stmt->fetch();
    echo "✅ اختبار الترميز العربي: " . $result['name_ar'] . "\n";

} catch (Exception $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "\n";
}
?>
```

### 4. إعداد النسخ الاحتياطي التلقائي

#### إنشاء سكريبت النسخ الاحتياطي:
```bash
#!/bin/bash
# ملف: backup_database.sh

# متغيرات قاعدة البيانات
DB_HOST="localhost"
DB_NAME="csdb"
DB_USER="backup_user"
DB_PASS="backup_password"

# مجلد النسخ الاحتياطية
BACKUP_DIR="/home/<USER>/database"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/csdb_backup_$DATE.sql"

# إنشاء المجلد إذا لم يكن موجوداً
mkdir -p $BACKUP_DIR

# إنشاء النسخة الاحتياطية
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --default-character-set=utf8mb4 \
    $DB_NAME > $BACKUP_FILE

# ضغط النسخة الاحتياطية
gzip $BACKUP_FILE

# حذف النسخ الاحتياطية الأقدم من 30 يوم
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "تم إنشاء النسخة الاحتياطية: $BACKUP_FILE.gz"
```

#### إضافة المهمة إلى crontab:
```bash
# تحرير crontab
crontab -e

# إضافة مهمة يومية في الساعة 2:00 صباحاً
0 2 * * * /path/to/backup_database.sh >> /var/log/database_backup.log 2>&1
```

### 5. مراقبة الأداء

```sql
-- تفعيل slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow.log';

-- مراقبة استخدام الفهارس
SELECT
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'csdb'
ORDER BY CARDINALITY DESC;
```

---

## 💾 نصائح للنسخ الاحتياطي المستقبلي

### 1. استراتيجية النسخ الاحتياطي

#### أ) النسخ الاحتياطي اليومي الكامل:
```bash
#!/bin/bash
# نسخة احتياطية كاملة يومية
mysqldump -h localhost -u backup_user -p \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --all-databases \
    --default-character-set=utf8mb4 \
    --result-file="/backups/full_backup_$(date +%Y%m%d).sql"
```

#### ب) النسخ الاحتياطي التزايدي:
```bash
# تفعيل binary logging
echo "log-bin=mysql-bin" >> /etc/mysql/my.cnf
echo "binlog-format=ROW" >> /etc/mysql/my.cnf

# نسخ احتياطية تزايدية
mysqlbinlog mysql-bin.000001 > incremental_backup.sql
```

#### ج) النسخ الاحتياطي للبيانات فقط:
```bash
# نسخ احتياطية للبيانات فقط (بدون البنية)
mysqldump -h localhost -u backup_user -p \
    --no-create-info \
    --single-transaction \
    --default-character-set=utf8mb4 \
    csdb > data_only_backup.sql
```

### 2. أتمتة النسخ الاحتياطي

#### سكريبت شامل للنسخ الاحتياطي:
```bash
#!/bin/bash
# ملف: comprehensive_backup.sh

# إعدادات
DB_HOST="localhost"
DB_NAME="csdb"
DB_USER="backup_user"
DB_PASS="backup_password"
BACKUP_BASE_DIR="/home/<USER>"
RETENTION_DAYS=30
EMAIL="<EMAIL>"

# إنشاء مجلدات النسخ الاحتياطي
DAILY_DIR="$BACKUP_BASE_DIR/daily"
WEEKLY_DIR="$BACKUP_BASE_DIR/weekly"
MONTHLY_DIR="$BACKUP_BASE_DIR/monthly"

mkdir -p $DAILY_DIR $WEEKLY_DIR $MONTHLY_DIR

# متغيرات التاريخ
DATE=$(date +%Y%m%d_%H%M%S)
DAY_OF_WEEK=$(date +%u)
DAY_OF_MONTH=$(date +%d)

# دالة إنشاء النسخة الاحتياطية
create_backup() {
    local backup_file=$1
    local backup_type=$2

    echo "بدء إنشاء النسخة الاحتياطية $backup_type..."

    mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --default-character-set=utf8mb4 \
        --add-drop-database \
        --databases $DB_NAME > $backup_file

    if [ $? -eq 0 ]; then
        gzip $backup_file
        echo "✅ تم إنشاء النسخة الاحتياطية بنجاح: $backup_file.gz"

        # إرسال إشعار بالنجاح
        echo "تم إنشاء النسخة الاحتياطية $backup_type بنجاح في $(date)" | \
        mail -s "نجح النسخ الاحتياطي - $backup_type" $EMAIL
    else
        echo "❌ فشل في إنشاء النسخة الاحتياطية"

        # إرسال إشعار بالفشل
        echo "فشل في إنشاء النسخة الاحتياطية $backup_type في $(date)" | \
        mail -s "فشل النسخ الاحتياطي - $backup_type" $EMAIL
        exit 1
    fi
}

# نسخة احتياطية يومية
create_backup "$DAILY_DIR/csdb_daily_$DATE.sql" "يومية"

# نسخة احتياطية أسبوعية (يوم الأحد)
if [ $DAY_OF_WEEK -eq 7 ]; then
    create_backup "$WEEKLY_DIR/csdb_weekly_$DATE.sql" "أسبوعية"
fi

# نسخة احتياطية شهرية (اليوم الأول من الشهر)
if [ $DAY_OF_MONTH -eq 01 ]; then
    create_backup "$MONTHLY_DIR/csdb_monthly_$DATE.sql" "شهرية"
fi

# تنظيف النسخ الاحتياطية القديمة
find $DAILY_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $WEEKLY_DIR -name "*.sql.gz" -mtime +$((RETENTION_DAYS * 4)) -delete
find $MONTHLY_DIR -name "*.sql.gz" -mtime +$((RETENTION_DAYS * 12)) -delete

echo "تم الانتهاء من عملية النسخ الاحتياطي"
```

### 3. النسخ الاحتياطي السحابي

#### أ) رفع إلى Google Drive:
```bash
# تثبيت rclone
curl https://rclone.org/install.sh | sudo bash

# إعداد Google Drive
rclone config

# رفع النسخة الاحتياطية
rclone copy /home/<USER>/daily/ gdrive:healthcare_backups/daily/
```

#### ب) رفع إلى AWS S3:
```bash
# تثبيت AWS CLI
pip install awscli

# إعداد AWS
aws configure

# رفع النسخة الاحتياطية
aws s3 sync /home/<USER>/ s3://your-backup-bucket/healthcare-backups/
```

#### ج) رفع إلى Dropbox:
```bash
# استخدام Dropbox Uploader
wget -O dropbox_uploader.sh "https://raw.githubusercontent.com/andreafabrizi/Dropbox-Uploader/master/dropbox_uploader.sh"
chmod +x dropbox_uploader.sh
./dropbox_uploader.sh

# رفع النسخة الاحتياطية
./dropbox_uploader.sh upload /home/<USER>/daily/ /healthcare_backups/
```

### 4. مراقبة النسخ الاحتياطي

#### سكريبت فحص سلامة النسخ الاحتياطية:
```bash
#!/bin/bash
# ملف: verify_backups.sh

BACKUP_DIR="/home/<USER>/daily"
TEST_DB="csdb_test"
EMAIL="<EMAIL>"

# العثور على أحدث نسخة احتياطية
LATEST_BACKUP=$(ls -t $BACKUP_DIR/*.sql.gz | head -1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "❌ لم يتم العثور على نسخ احتياطية"
    exit 1
fi

echo "فحص النسخة الاحتياطية: $LATEST_BACKUP"

# إنشاء قاعدة بيانات اختبار
mysql -u root -p -e "DROP DATABASE IF EXISTS $TEST_DB;"
mysql -u root -p -e "CREATE DATABASE $TEST_DB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# استعادة النسخة الاحتياطية في قاعدة الاختبار
gunzip -c $LATEST_BACKUP | mysql -u root -p $TEST_DB

if [ $? -eq 0 ]; then
    # فحص سلامة البيانات
    TABLE_COUNT=$(mysql -u root -p $TEST_DB -e "SHOW TABLES;" | wc -l)
    USER_COUNT=$(mysql -u root -p $TEST_DB -e "SELECT COUNT(*) FROM users;" | tail -1)

    echo "✅ النسخة الاحتياطية سليمة"
    echo "عدد الجداول: $((TABLE_COUNT - 1))"
    echo "عدد المستخدمين: $USER_COUNT"

    # إرسال تقرير النجاح
    echo "تم فحص النسخة الاحتياطية بنجاح في $(date)" | \
    mail -s "فحص النسخة الاحتياطية - نجح" $EMAIL
else
    echo "❌ النسخة الاحتياطية تالفة"

    # إرسال تقرير الفشل
    echo "النسخة الاحتياطية تالفة - تم الفحص في $(date)" | \
    mail -s "فحص النسخة الاحتياطية - فشل" $EMAIL
fi

# حذف قاعدة الاختبار
mysql -u root -p -e "DROP DATABASE $TEST_DB;"
```

### 5. إعداد مهام cron شاملة

```bash
# تحرير crontab
crontab -e

# إضافة المهام التالية:

# نسخة احتياطية يومية في الساعة 2:00 صباحاً
0 2 * * * /home/<USER>/comprehensive_backup.sh >> /var/log/backup.log 2>&1

# فحص سلامة النسخ الاحتياطية في الساعة 4:00 صباحاً
0 4 * * * /home/<USER>/verify_backups.sh >> /var/log/backup_verify.log 2>&1

# رفع النسخ الاحتياطية للسحابة في الساعة 5:00 صباحاً
0 5 * * * rclone sync /home/<USER>/ gdrive:healthcare_backups/ >> /var/log/cloud_sync.log 2>&1

# تنظيف السجلات القديمة أسبوعياً
0 3 * * 0 find /var/log/ -name "*.log" -mtime +30 -delete
```

---

## 📋 قائمة مراجعة ما بعد الاستعادة

### ✅ قائمة التحقق الأساسية:
- [ ] تم إنشاء قاعدة البيانات بالترميز الصحيح
- [ ] تم استيراد جميع الجداول (22 جدول)
- [ ] تم استيراد البيانات الافتراضية
- [ ] تعمل جميع العلاقات والمفاتيح الخارجية
- [ ] تعمل جميع الفهارس بشكل صحيح
- [ ] تعمل جميع الـ Views

### ✅ قائمة التحقق من الأمان:
- [ ] تم إنشاء مستخدمين منفصلين للتطبيق والنسخ الاحتياطي
- [ ] تم تقييد صلاحيات المستخدمين
- [ ] تم تحديث كلمات المرور الافتراضية
- [ ] تم تفعيل SSL للاتصالات
- [ ] تم إعداد جدار الحماية

### ✅ قائمة التحقق من الأداء:
- [ ] تم تحسين إعدادات MySQL
- [ ] تم تفعيل slow query log
- [ ] تم مراقبة استخدام الفهارس
- [ ] تم إعداد مراقبة الأداء

### ✅ قائمة التحقق من النسخ الاحتياطي:
- [ ] تم إعداد النسخ الاحتياطي التلقائي
- [ ] تم اختبار عملية الاستعادة
- [ ] تم إعداد النسخ الاحتياطي السحابي
- [ ] تم إعداد مراقبة النسخ الاحتياطي

---

## 🎯 الخلاصة

تم إنشاء دليل شامل لاستعادة قاعدة البيانات يتضمن:

1. **الخطوات التحضيرية**: فحص المتطلبات وإعداد البيئة
2. **طرق الاستعادة المتعددة**: سطر الأوامر، CloudPanel، phpMyAdmin، أدوات إدارة قواعد البيانات
3. **التحقق من النجاح**: فحص شامل للجداول والبيانات والعلاقات
4. **استكشاف الأخطاء**: حلول للمشاكل الشائعة
5. **خطوات ما بعد الاستعادة**: إعداد الصلاحيات والأمان
6. **النسخ الاحتياطي المستقبلي**: استراتيجيات شاملة للحماية

### 🚀 الخطوات السريعة للاستعادة:
```bash
# 1. إنشاء قاعدة البيانات
mysql -u root -p -e "CREATE DATABASE csdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 2. استيراد البيانات
mysql -u csdbuser -p csdb < database_complete_rebuild.sql

# 3. التحقق من النجاح
mysql -u csdbuser -p csdb -e "SHOW TABLES; SELECT COUNT(*) FROM users;"
```

الآن لديك دليل شامل لاستعادة قاعدة البيانات بأمان وفعالية! 🎉
