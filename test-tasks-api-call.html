<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المهام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API المهام</h1>
        
        <div class="test-section">
            <h3>1. تحميل المهام</h3>
            <button onclick="testLoadTasks()">تحميل مهام المستخدم admin</button>
            <div id="loadResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. إضافة مهمة جديدة</h3>
            <button onclick="testSaveTask()">إضافة مهمة تجريبية</button>
            <div id="saveResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. تحديث حالة المهمة</h3>
            <button onclick="testToggleTask()">تغيير حالة آخر مهمة</button>
            <div id="toggleResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. حذف مهمة</h3>
            <button onclick="testDeleteTask()">حذف آخر مهمة</button>
            <div id="deleteResult" class="result"></div>
        </div>
    </div>

    <script>
        let lastTaskId = null;

        async function testLoadTasks() {
            const resultDiv = document.getElementById('loadResult');
            resultDiv.textContent = 'جاري التحميل...';
            
            try {
                const response = await fetch('tasks-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load',
                        user_id: 'admin'
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
                if (data.success && data.tasks.length > 0) {
                    lastTaskId = data.tasks[data.tasks.length - 1].id;
                }
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testSaveTask() {
            const resultDiv = document.getElementById('saveResult');
            resultDiv.textContent = 'جاري الإضافة...';
            
            try {
                const response = await fetch('tasks-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save',
                        user_id: 'admin',
                        title: 'مهمة تجريبية ' + new Date().toLocaleString('ar'),
                        description: 'وصف المهمة التجريبية',
                        priority: 'medium',
                        due_date: '2024-12-31'
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
                if (data.success && data.task) {
                    lastTaskId = data.task.id;
                }
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testToggleTask() {
            const resultDiv = document.getElementById('toggleResult');
            
            if (!lastTaskId) {
                resultDiv.textContent = 'يجب تحميل المهام أولاً أو إضافة مهمة جديدة';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = 'جاري التحديث...';
            
            try {
                const response = await fetch('tasks-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'toggle_completion',
                        task_id: lastTaskId,
                        completed: true
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testDeleteTask() {
            const resultDiv = document.getElementById('deleteResult');
            
            if (!lastTaskId) {
                resultDiv.textContent = 'يجب تحميل المهام أولاً أو إضافة مهمة جديدة';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = 'جاري الحذف...';
            
            try {
                const response = await fetch('tasks-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        task_id: lastTaskId
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
                if (data.success) {
                    lastTaskId = null;
                }
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
