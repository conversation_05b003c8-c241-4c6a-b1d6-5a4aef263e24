<?php
/**
 * فحص المستخدمين الموجودين
 */

require_once 'config/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // عرض جميع المستخدمين
    $stmt = $conn->query('SELECT id, username, full_name, role, center_id FROM users ORDER BY id');
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "👥 المستخدمون الموجودون:\n";
    echo "=====================================\n";
    
    if (empty($users)) {
        echo "❌ لا يوجد مستخدمون في قاعدة البيانات\n";
    } else {
        foreach ($users as $user) {
            echo "🆔 ID: {$user['id']}\n";
            echo "👤 اسم المستخدم: {$user['username']}\n";
            echo "📝 الاسم الكامل: {$user['full_name']}\n";
            echo "🎭 الدور: {$user['role']}\n";
            echo "🏥 المركز: {$user['center_id']}\n";
            echo "-------------------------------------\n";
        }
    }
    
    echo "\n📊 إجمالي عدد المستخدمين: " . count($users) . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
