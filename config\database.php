<?php
/**
 * إعدادات قاعدة البيانات لنظام إدارة المراكز الصحية
 * Healthcare Centers Management System Database Configuration
 */

class Database {
    private $host = '127.0.0.1';
    private $db_name = 'csdb';
    private $username = 'csdbuser';
    private $password = 'j5aKN6lz5bsujTcWaYAd';
    private $charset = 'utf8mb4';
    private $conn;

    /**
     * الحصول على اتصال قاعدة البيانات
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }

        return $this->conn;
    }

    /**
     * إغلاق اتصال قاعدة البيانات
     * Close database connection
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * تنفيذ استعلام SQL
     * Execute SQL query
     */
    public function executeQuery($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $exception) {
            error_log("Query error: " . $exception->getMessage());
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }

    /**
     * الحصول على آخر ID مُدرج
     * Get last inserted ID
     */
    public function getLastInsertId() {
        return $this->conn->lastInsertId();
    }

    /**
     * بدء معاملة
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     * Commit transaction
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * إلغاء المعاملة
     * Rollback transaction
     */
    public function rollback() {
        return $this->conn->rollback();
    }

    /**
     * فحص وجود الجداول
     * Check if tables exist
     */
    public function checkTablesExist() {
        $tables = [
            'centers', 'users', 'children', 'vaccines', 'vaccine_stock',
            'child_vaccinations', 'vaccine_usage_log', 'medicines', 'medicine_stock',
            'contraceptives', 'contraceptive_stock', 'monthly_planning',
            'messages', 'tasks', 'notifications', 'monthly_stats', 'user_settings'
        ];

        $existing_tables = [];
        
        try {
            $stmt = $this->conn->query("SHOW TABLES");
            while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
                $existing_tables[] = $row[0];
            }
        } catch(PDOException $exception) {
            error_log("Error checking tables: " . $exception->getMessage());
            return false;
        }

        foreach ($tables as $table) {
            if (!in_array($table, $existing_tables)) {
                return false;
            }
        }

        return true;
    }

    /**
     * إنشاء قاعدة البيانات والجداول
     * Create database and tables
     */
    public function createDatabase() {
        try {
            // قراءة ملف SQL
            $sql_file = __DIR__ . '/../database_design.sql';
            if (!file_exists($sql_file)) {
                throw new Exception("ملف قاعدة البيانات غير موجود");
            }

            $sql = file_get_contents($sql_file);
            
            // تقسيم الاستعلامات
            $queries = explode(';', $sql);
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    $this->conn->exec($query);
                }
            }

            return true;
            
        } catch(PDOException $exception) {
            error_log("Database creation error: " . $exception->getMessage());
            throw new Exception("فشل في إنشاء قاعدة البيانات: " . $exception->getMessage());
        }
    }

    /**
     * تنظيف البيانات القديمة
     * Clean old data
     */
    public function cleanOldData($days = 30) {
        try {
            $this->beginTransaction();

            // حذف الإشعارات القديمة المقروءة
            $sql = "DELETE FROM notifications WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $this->executeQuery($sql, [$days]);

            // حذف الرسائل القديمة (اختياري)
            // $sql = "DELETE FROM messages WHERE sent_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            // $this->executeQuery($sql, [$days * 6]); // 6 أشهر

            $this->commit();
            return true;

        } catch(Exception $e) {
            $this->rollback();
            error_log("Clean old data error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء نسخة احتياطية من البيانات
     * Create data backup
     */
    public function createBackup($backup_path = null) {
        if (!$backup_path) {
            $backup_path = __DIR__ . '/../backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
        }

        $backup_dir = dirname($backup_path);
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }

        try {
            $command = sprintf(
                'mysqldump --user=%s --password=%s --host=%s %s > %s',
                escapeshellarg($this->username),
                escapeshellarg($this->password),
                escapeshellarg($this->host),
                escapeshellarg($this->db_name),
                escapeshellarg($backup_path)
            );

            exec($command, $output, $return_code);

            if ($return_code === 0) {
                return $backup_path;
            } else {
                throw new Exception("فشل في إنشاء النسخة الاحتياطية");
            }

        } catch(Exception $e) {
            error_log("Backup error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * استعادة النسخة الاحتياطية
     * Restore backup
     */
    public function restoreBackup($backup_path) {
        if (!file_exists($backup_path)) {
            throw new Exception("ملف النسخة الاحتياطية غير موجود");
        }

        try {
            $command = sprintf(
                'mysql --user=%s --password=%s --host=%s %s < %s',
                escapeshellarg($this->username),
                escapeshellarg($this->password),
                escapeshellarg($this->host),
                escapeshellarg($this->db_name),
                escapeshellarg($backup_path)
            );

            exec($command, $output, $return_code);

            if ($return_code === 0) {
                return true;
            } else {
                throw new Exception("فشل في استعادة النسخة الاحتياطية");
            }

        } catch(Exception $e) {
            error_log("Restore error: " . $e->getMessage());
            return false;
        }
    }
}

// إعدادات إضافية للبيئة
if (!defined('DB_CONFIG_LOADED')) {
    define('DB_CONFIG_LOADED', true);
    
    // تعيين المنطقة الزمنية
    date_default_timezone_set('Africa/Casablanca');
    
    // إعدادات PHP للتعامل مع UTF-8
    ini_set('default_charset', 'utf-8');
    mb_internal_encoding('UTF-8');
    mb_http_output('UTF-8');
}
?>
