<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعادة التوجيه</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 600px;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .status {
            font-size: 1.2em;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
        }
        .success {
            background: rgba(40, 167, 69, 0.3);
            border: 2px solid #28a745;
        }
        .info {
            background: rgba(23, 162, 184, 0.3);
            border: 2px solid #17a2b8;
        }
        .links {
            margin: 30px 0;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .current-url {
            font-family: monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 اختبار إعادة التوجيه</h1>
        
        <div class="status success">
            ✅ إعادة التوجيه تعمل بشكل صحيح!
        </div>
        
        <div class="status info">
            📍 الرابط الحالي:
            <div class="current-url" id="currentUrl"></div>
        </div>
        
        <div class="status info">
            🎯 إذا كنت ترى هذه الصفحة، فهذا يعني أن:
            <br>• تم إعادة توجيهك تلقائياً إلى النسخة الآمنة
            <br>• شهادة SSL تعمل بشكل صحيح
            <br>• الموقع متاح من جميع الأجهزة
        </div>
        
        <div class="links">
            <h3>🔗 روابط الاختبار:</h3>
            <a href="https://www.csmanager.online/">الصفحة الرئيسية</a>
            <a href="https://www.csmanager.online/index.html">نظام إدارة التلقيح</a>
            <a href="https://www.csmanager.online/test-tasks-api-call.html">اختبار API المهام</a>
        </div>
        
        <div class="status info">
            📱 اختبر الروابط التالية من أجهزة مختلفة:
            <br>• <code>https://csmanager.online</code> (بدون www)
            <br>• <code>http://csmanager.online</code> (HTTP)
            <br>• <code>http://www.csmanager.online</code> (HTTP مع www)
            <br><br>
            جميعها يجب أن تعيد التوجيه إلى: <code>https://www.csmanager.online</code>
        </div>
    </div>

    <script>
        // عرض الرابط الحالي
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // فحص إعادة التوجيه
        if (window.location.protocol === 'https:' && window.location.hostname === 'www.csmanager.online') {
            console.log('✅ إعادة التوجيه تعمل بشكل صحيح');
        } else {
            console.log('⚠️ قد تحتاج لفحص إعدادات إعادة التوجيه');
        }
        
        // إضافة معلومات إضافية
        setTimeout(() => {
            const info = document.createElement('div');
            info.className = 'status info';
            info.innerHTML = `
                🔍 معلومات تقنية:
                <br>• البروتوكول: ${window.location.protocol}
                <br>• النطاق: ${window.location.hostname}
                <br>• المنفذ: ${window.location.port || 'افتراضي'}
                <br>• المسار: ${window.location.pathname}
            `;
            document.querySelector('.container').appendChild(info);
        }, 1000);
    </script>
</body>
</html>
