<?php
/**
 * API إدارة بيانات الأطفال والتلقيحات
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين إعدادات قاعدة البيانات
require_once 'config/database-live.php';

try {
    // الاتصال بقاعدة البيانات باستخدام الإعدادات المناسبة
    $pdo = getDatabaseConnection();
    
    // إنشاء جدول الأطفال إذا لم يكن موجوداً
    createChildrenTable($pdo);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $action = $input['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'load':
            loadChildren($pdo, $input);
            break;
            
        case 'save':
            saveChild($pdo, $input);
            break;
            
        case 'update':
            updateChild($pdo, $input);
            break;
            
        case 'delete':
            deleteChild($pdo, $input);
            break;
            
        case 'load_all_center':
            loadAllCenterChildren($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    error_log('خطأ في children-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'file' => 'children-api.php',
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ], JSON_UNESCAPED_UNICODE);
}

// إنشاء جدول الأطفال
function createChildrenTable($pdo) {
    try {
        // فحص إذا كان الجدول موجود
        $stmt = $pdo->query("SHOW TABLES LIKE 'children'");
        if ($stmt->rowCount() > 0) {
            return; // الجدول موجود
        }

        $sql = "
        CREATE TABLE children (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            name VARCHAR(255) NOT NULL,
            birth_date DATE NOT NULL,
            vaccination_dates TEXT,
            completed_vaccinations TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id),
            INDEX idx_birth_date (birth_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        $pdo->exec($sql);

    } catch (Exception $e) {
        // إذا فشل إنشاء الجدول، تجاهل الخطأ
        error_log('خطأ في إنشاء جدول الأطفال: ' . $e->getMessage());
    }
}

// تحميل الأطفال
function loadChildren($pdo, $input) {
    $user_id = $input['user_id'] ?? '';

    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }

    try {
        // فحص إذا كان عمود user_id موجود
        $stmt = $pdo->query("SHOW COLUMNS FROM children LIKE 'user_id'");
        $userIdExists = $stmt->rowCount() > 0;

        if ($userIdExists) {
            $stmt = $pdo->prepare("
                SELECT c.*, u.name as nurse_name
                FROM children c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE c.user_id = ?
                ORDER BY c.created_at DESC
            ");
            $stmt->execute([$user_id]);
        } else {
            // استخدام nurse_id بدلاً من user_id إذا لم يكن user_id موجود
            $stmt = $pdo->prepare("
                SELECT c.*, u.name as nurse_name
                FROM children c
                LEFT JOIN users u ON c.nurse_id = u.id
                WHERE c.nurse_id = ?
                ORDER BY c.created_at DESC
            ");
            $stmt->execute([$user_id]);
        }

        $children = $stmt->fetchAll();
    } catch (Exception $e) {
        // إذا كان هناك خطأ، إرجاع قائمة فارغة
        echo json_encode([
            'success' => true,
            'children' => [],
            'message' => 'خطأ في تحميل البيانات: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // تحويل البيانات للتوافق مع الواجهة
    $formatted_children = [];
    foreach ($children as $child) {
        $formatted_children[] = [
            'id' => $child['id'],
            'name' => $child['name'],
            'birthDate' => $child['birth_date'],
            'vaccinationDates' => json_decode($child['vaccination_dates'] ?? '[]', true),
            'completedVaccinations' => json_decode($child['completed_vaccinations'] ?? '{}', true),
            'nurseName' => $child['nurse_name'],
            'createdAt' => $child['created_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'children' => $formatted_children
    ], JSON_UNESCAPED_UNICODE);
}

// حفظ طفل جديد
function saveChild($pdo, $input) {
    error_log('saveChild called with input: ' . json_encode($input));

    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '';
    $name = $input['name'] ?? '';
    $birth_date = $input['birth_date'] ?? '';
    $vaccination_dates = $input['vaccination_dates'] ?? [];
    $completed_vaccinations = $input['completed_vaccinations'] ?? [];

    // تحويل تاريخ الميلاد إلى صيغة MySQL إذا لزم الأمر
    if ($birth_date && strpos($birth_date, '/') !== false) {
        $parts = explode('/', $birth_date);
        if (count($parts) === 3) {
            $birth_date = $parts[2] . '-' . str_pad($parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($parts[0], 2, '0', STR_PAD_LEFT);
        }
    }

    error_log("saveChild data: user_id=$user_id, center_id=$center_id, name=$name, birth_date=$birth_date");

    if (!$user_id || !$center_id || !$name || !$birth_date) {
        throw new Exception('جميع البيانات الأساسية مطلوبة: user_id, center_id, name, birth_date');
    }

    // التحقق من صحة تنسيق التاريخ
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $birth_date)) {
        throw new Exception('تنسيق تاريخ الميلاد غير صحيح. يجب أن يكون YYYY-MM-DD');
    }
    
    try {
        // التحقق من عدم وجود طفل بنفس الاسم وتاريخ الميلاد
        $stmt = $pdo->prepare("
            SELECT id FROM children
            WHERE user_id = ? AND name = ? AND birth_date = ?
        ");
        $stmt->execute([$user_id, $name, $birth_date]);

        if ($stmt->fetch()) {
            throw new Exception('يوجد طفل بنفس الاسم وتاريخ الميلاد');
        }
    } catch (Exception $e) {
        // إذا كان الجدول غير موجود، أنشئه أولاً
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            createChildrenTable($pdo);
        } else {
            throw $e;
        }
    }
    
    $child_id = $input['id'] ?? (time() . '_' . uniqid());
    
    // فحص الأعمدة الموجودة وإنشاء الاستعلام المناسب
    $stmt = $pdo->query("SHOW COLUMNS FROM children");
    $columns = $stmt->fetchAll();
    $columnNames = array_column($columns, 'Field');

    $insertColumns = ['id', 'name', 'birth_date'];
    $insertValues = [$child_id, $name, $birth_date];
    $placeholders = ['?', '?', '?'];

    // إضافة الأعمدة الموجودة
    if (in_array('user_id', $columnNames)) {
        $insertColumns[] = 'user_id';
        $insertValues[] = $user_id;
        $placeholders[] = '?';
    } elseif (in_array('nurse_id', $columnNames)) {
        $insertColumns[] = 'nurse_id';
        $insertValues[] = $user_id;
        $placeholders[] = '?';
    }

    if (in_array('center_id', $columnNames)) {
        $insertColumns[] = 'center_id';
        // تحويل center_id إلى رقم إذا كان العمود من نوع INT
        $centerIdColumn = array_filter($columns, function($col) {
            return $col['Field'] === 'center_id';
        });
        if (!empty($centerIdColumn)) {
            $centerIdType = reset($centerIdColumn)['Type'];
            if (strpos($centerIdType, 'int') !== false) {
                $insertValues[] = is_numeric($center_id) ? (int)$center_id : 1; // قيمة افتراضية
            } else {
                $insertValues[] = $center_id;
            }
        } else {
            $insertValues[] = $center_id;
        }
        $placeholders[] = '?';
    }

    if (in_array('vaccination_dates', $columnNames)) {
        $insertColumns[] = 'vaccination_dates';
        $insertValues[] = json_encode($vaccination_dates, JSON_UNESCAPED_UNICODE);
        $placeholders[] = '?';
    }

    if (in_array('completed_vaccinations', $columnNames)) {
        $insertColumns[] = 'completed_vaccinations';
        $insertValues[] = json_encode($completed_vaccinations, JSON_UNESCAPED_UNICODE);
        $placeholders[] = '?';
    }

    $sql = "INSERT INTO children (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $placeholders) . ")";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($insertValues);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ بيانات الطفل بنجاح',
        'child_id' => $child_id
    ], JSON_UNESCAPED_UNICODE);
}

// تحديث بيانات طفل
function updateChild($pdo, $input) {
    $child_id = $input['child_id'] ?? '';
    $vaccination_dates = $input['vaccination_dates'] ?? [];
    $completed_vaccinations = $input['completed_vaccinations'] ?? [];
    
    if (!$child_id) {
        throw new Exception('معرف الطفل مطلوب');
    }
    
    // فحص الأعمدة الموجودة
    $stmt = $pdo->query("SHOW COLUMNS FROM children");
    $columns = $stmt->fetchAll();
    $columnNames = array_column($columns, 'Field');

    $updateParts = [];
    $updateValues = [];

    if (in_array('vaccination_dates', $columnNames)) {
        $updateParts[] = 'vaccination_dates = ?';
        $updateValues[] = json_encode($vaccination_dates, JSON_UNESCAPED_UNICODE);
    }

    if (in_array('completed_vaccinations', $columnNames)) {
        $updateParts[] = 'completed_vaccinations = ?';
        $updateValues[] = json_encode($completed_vaccinations, JSON_UNESCAPED_UNICODE);
    }

    if (in_array('updated_at', $columnNames)) {
        $updateParts[] = 'updated_at = CURRENT_TIMESTAMP';
    }

    if (empty($updateParts)) {
        throw new Exception('لا توجد أعمدة للتحديث');
    }

    $updateValues[] = $child_id;
    $sql = "UPDATE children SET " . implode(', ', $updateParts) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($updateValues);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث بيانات الطفل بنجاح'
    ], JSON_UNESCAPED_UNICODE);
}

// حذف طفل
function deleteChild($pdo, $input) {
    $child_id = $input['child_id'] ?? '';
    
    if (!$child_id) {
        throw new Exception('معرف الطفل مطلوب');
    }
    
    $stmt = $pdo->prepare("DELETE FROM children WHERE id = ?");
    $stmt->execute([$child_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف الطفل بنجاح'
    ], JSON_UNESCAPED_UNICODE);
}

// تحميل جميع أطفال المركز
function loadAllCenterChildren($pdo, $input) {
    $center_id = $input['center_id'] ?? '';
    
    if (!$center_id) {
        throw new Exception('معرف المركز مطلوب');
    }
    
    $stmt = $pdo->prepare("
        SELECT c.*, u.name as nurse_name
        FROM children c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.center_id = ?
        ORDER BY c.created_at DESC
    ");
    
    $stmt->execute([$center_id]);
    $children = $stmt->fetchAll();
    
    // تحويل البيانات للتوافق مع الواجهة
    $formatted_children = [];
    foreach ($children as $child) {
        $formatted_children[] = [
            'id' => $child['id'],
            'name' => $child['name'],
            'birthDate' => $child['birth_date'],
            'vaccinationDates' => json_decode($child['vaccination_dates'] ?? '[]', true),
            'completedVaccinations' => json_decode($child['completed_vaccinations'] ?? '{}', true),
            'nurseName' => $child['nurse_name'],
            'createdAt' => $child['created_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'children' => $formatted_children
    ], JSON_UNESCAPED_UNICODE);
}
?>
