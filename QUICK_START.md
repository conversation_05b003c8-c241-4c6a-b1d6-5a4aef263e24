# دليل البدء السريع - استعادة قاعدة البيانات

## 🚀 الطريقة السريعة (باستخدام السكريبت التلقائي)

### 1. تشغيل السكريبت التلقائي:
```bash
# منح صلاحية التنفيذ
chmod +x restore_database.sh

# تشغيل السكريبت بالإعدادات الافتراضية
./restore_database.sh

# أو مع إنشاء نسخة احتياطية
./restore_database.sh -b

# أو فحص الاتصال فقط
./restore_database.sh -c
```

### 2. مع معاملات مخصصة:
```bash
./restore_database.sh -h localhost -d csdb -u csdbuser -p your_password
```

---

## ⚡ الطريقة اليدوية السريعة

### 1. إنشاء قاعدة البيانات:
```sql
mysql -u root -p -e "CREATE DATABASE csdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 2. استيراد البيانات:
```bash
mysql -u csdbuser -p csdb < database_complete_rebuild.sql
```

### 3. التحقق من النجاح:
```sql
mysql -u csdbuser -p csdb -e "SHOW TABLES; SELECT COUNT(*) FROM users;"
```

---

## 🔧 إعدادات قاعدة البيانات

- **اسم قاعدة البيانات**: `csdb`
- **المستخدم**: `csdbuser`
- **كلمة المرور**: `j5aKN6lz5bsujTcWaYAd`
- **الترميز**: `utf8mb4_unicode_ci`

---

## 📋 التحقق من النجاح

بعد الاستعادة، تحقق من:
- عدد الجداول: 22 جدول
- عدد المستخدمين: 4 مستخدمين
- عدد اللقاحات: 19 لقاح
- عدد المراكز: 3 مراكز

---

## 🆘 في حالة المشاكل

1. **خطأ في الاتصال**: تحقق من معاملات الاتصال
2. **خطأ في الصلاحيات**: امنح صلاحيات إضافية للمستخدم
3. **خطأ في الترميز**: تأكد من دعم utf8mb4
4. **راجع الملفات**:
   - `database_restoration_guide.md` - دليل مفصل
   - `import_errors.log` - سجل الأخطاء
   - `database_analysis_report.md` - تقرير التحليل

---

## 📞 الدعم

للحصول على مساعدة مفصلة، راجع:
- `database_restoration_guide.md` - الدليل الشامل
- `database_analysis_report.md` - تقرير التحليل الكامل

---

## ✅ قائمة مراجعة سريعة

- [ ] تم تشغيل السكريبت بنجاح
- [ ] تم إنشاء 22 جدول
- [ ] تم استيراد البيانات الافتراضية
- [ ] تم اختبار الاتصال من التطبيق
- [ ] تم إعداد النسخ الاحتياطي التلقائي
