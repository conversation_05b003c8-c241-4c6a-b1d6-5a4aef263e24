# إعدادات Apache لنظام إدارة المراكز الصحية
# Apache Configuration for Healthcare Centers Management System

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# إعدادات الأمان
# Security Settings

# منع الوصول للملفات الحساسة
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول لملفات الإعداد
<FilesMatch "\.(ini|log|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلد config مباشرة
RewriteRule ^config/ - [F,L]

# إعدادات CORS للـ API
# CORS Settings for API

# السماح بطلبات من جميع المصادر (يمكن تخصيصها حسب الحاجة)
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
Header always set Access-Control-Max-Age "3600"

# التعامل مع طلبات OPTIONS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# إعادة توجيه طلبات API
# API Request Routing

# توجيه جميع طلبات /api إلى api/index.php
RewriteCond %{REQUEST_URI} ^/api/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# إعدادات PHP
# PHP Settings

# زيادة حد الذاكرة
php_value memory_limit 256M

# زيادة وقت التنفيذ
php_value max_execution_time 300

# زيادة حجم الملفات المرفوعة
php_value upload_max_filesize 50M
php_value post_max_size 50M

# تفعيل عرض الأخطاء في بيئة التطوير (يجب إيقافها في الإنتاج)
php_value display_errors 1
php_value error_reporting E_ALL

# إعدادات الجلسات
php_value session.gc_maxlifetime 3600
php_value session.cookie_lifetime 3600

# إعدادات التخزين المؤقت
# Caching Settings

# تفعيل ضغط gzip
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# إعدادات انتهاء الصلاحية للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات الأمان الإضافية
# Additional Security Settings

# منع عرض محتويات المجلدات
Options -Indexes

# إخفاء معلومات الخادم
ServerTokens Prod
Header unset Server
Header always unset X-Powered-By

# حماية من XSS
Header always set X-XSS-Protection "1; mode=block"

# منع تضمين الموقع في إطارات خارجية
Header always set X-Frame-Options "SAMEORIGIN"

# فرض HTTPS وإعادة توجيه إلى www
# Force HTTPS and redirect to www
RewriteCond %{HTTPS} off [OR]
RewriteCond %{HTTP_HOST} ^csmanager\.online$ [NC]
RewriteRule ^(.*)$ https://www.csmanager.online/$1 [R=301,L]

# إعادة توجيه الصفحة الرئيسية
# Homepage Redirect

# توجيه الصفحة الرئيسية إلى النسخة الجديدة
RewriteCond %{REQUEST_URI} ^/$
RewriteRule ^$ cs-manager-api.html [L]

# إعدادات معالجة الأخطاء
# Error Handling

# صفحات الأخطاء المخصصة
ErrorDocument 404 /error-pages/404.html
ErrorDocument 500 /error-pages/500.html
ErrorDocument 403 /error-pages/403.html

# تسجيل الأخطاء
LogLevel warn
CustomLog logs/access.log combined
ErrorLog logs/error.log

# إعدادات خاصة بقاعدة البيانات
# Database Related Settings

# منع الوصول لملفات النسخ الاحتياطية
<FilesMatch "\.(sql|bak|backup)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# إعدادات التحميل والرفع
# Upload and Download Settings

# السماح بأنواع ملفات معينة فقط
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# منع تنفيذ ملفات PHP في مجلد الرفع
<Directory "uploads/">
    php_flag engine off
    AddType text/plain .php .php3 .phtml .pht
</Directory>

# إعدادات خاصة بالـ API
# API Specific Settings

# تعيين نوع المحتوى للـ API
<LocationMatch "^/api/">
    Header set Content-Type "application/json; charset=utf-8"
</LocationMatch>

# إعدادات التحسين
# Optimization Settings

# تفعيل Keep-Alive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# ضغط الاستجابات
<IfModule mod_deflate.c>
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# إعدادات الصيانة
# Maintenance Settings

# تفعيل وضع الصيانة (إلغاء التعليق عند الحاجة)
# RewriteCond %{REQUEST_URI} !/maintenance.html$
# RewriteCond %{REMOTE_ADDR} !^123\.456\.789\.000$
# RewriteRule $ /maintenance.html [R=302,L]

# نهاية الملف
# End of file
